import { Card, CardContent, Typography, Box } from "@mui/material";

class BasicInfo {
  lastName = "";
  firstName = "";
  lastNameKana = "";
  firstNameKana = "";
  email = "";
  phoneNo = "";
  gender = "";
  password = "";
  birthdayYear = "";
  birthdayMonth = "";
}

class ProfileCard {
  title = "";
  description = "";
}

export default function UserProfile() {
  const profiles = [
    {
      title: "Basic Information",
      description: "Personal details and contact information",
    },
    {
      title: "Work Experience",
      description: "Professional background and career history",
    },
    {
      title: "Skills & Qualifications",
      description: "Technical skills and certifications",
    },
    { title: "Preferences", description: "Job preferences and requirements" },
  ];

  return (
    <Box sx={{ display: "flex", flexDirection: "column", gap: 3 }}>
      {profiles.map((profile, index) => (
        <Card key={index}>
          <CardContent>
            <Typography variant="h6" component="h2" gutterBottom>
              {profile.title}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {profile.description}
            </Typography>
          </CardContent>
        </Card>
      ))}
    </Box>
  );
}
