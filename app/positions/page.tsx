"use client";

import { useCallback, useEffect, useState, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import {
  Box,
  Typography,
  Modal,
  Fab,
  CircularProgress,
  Button,
} from "@mui/material";
import ChatIcon from "@mui/icons-material/Chat";
import { POSITION_DETAIL_TABS } from "@/constants/talks/detail";
import PositionRecord from "@/models/records/Position";
import ApplyCompanyRecord from "@/models/records/ApplyCompany";
import BusinessRecord from "@/models/records/Business";
import InterviewRecord from "@/models/records/Interview";
import PositionTop from "@/components/positions/detail/tabs/PositionTop.jsx";
import JobDescription from "@/components/positions/detail/tabs/JobDescription.jsx";
// import InterviewSettings from "@/components/positions/detail/tabs/InterviewSettings.jsx";
import CompanyOverview from "@/components/positions/detail/tabs/CompanyOverview.jsx";
import styles from "./page.module.scss";
import ModalBadgeDescription from "@/components/app/shared/ModalBadgeDescription";
import ModalHPMCertificationDescription from "@/components/positions/detail/tabs/ModalHPMCertificationDescription";
import Chat from "@/components/Chat";
import { PAGE_NAME } from "@/constants/page";
import { sendWebSocketMessage } from "@/lib/socket";
import { useAppDispatch } from "@/lib/store/hooks";
import { setSessionStatus } from "@/lib/store/features/websocket/websocketSlice";
import { SessionStatus } from "@/constants/enum";
import { getBusinessData, getCompanyData, getPositionData } from "./getPositionData";

function PositionDetail() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const positionId = searchParams.get("positionId");

  // ローディング
  const [isPositionLoading, setIsPositionLoading] = useState(true);
  const [isCompanyLoading, setIsCompanyLoading] = useState(true);
  const [isBusinessLoading, setIsBusinessLoading] = useState(true);

  // エラー
  const [error, setError] = useState<string | null>(null);

  // チャットモーダルダイアログ
  const [isChatOpen, setIsChatOpen] = useState(false);

  // データ
  const [positionImtRecord, setPositionImtRecord] =
    useState<PositionRecord | null>(null);
  const [companyImtRecord, setCompanyImtRecord] =
    useState<ApplyCompanyRecord | null>(null);
  const [businessImtRecord, setBusinessImtRecord] =
    useState<BusinessRecord | null>(null);
  const [interviewImtRecord, setInterviewImtRecord] = useState<InterviewRecord>(
    new InterviewRecord({})
  );
  const [isOutsourcingPosition, setIsOutsourcingPosition] = useState(false);
  const [isRegularOutsourcingPosition, setIsRegularOutsourcingPosition] =
    useState(false);
  const [isSpotOutsourcingPosition, setIsSpotOutsourcingPosition] =
    useState(false);
  const [isCommissionOutsourcingPosition, setIsCommissionOutsourcingPosition] =
    useState(false);

  const [isBadgeDescriptionModalDisplay, setIsBadgeDescriptionModalDisplay] =
    useState(false);
  const [
    isHPMCertificationDescriptionModalDisplay,
    setIsHPMCertificationDescriptionModalDisplay,
  ] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      if (!positionId) {
        setError("求人が見つかりませんでした");
        setIsPositionLoading(false);
        setIsCompanyLoading(false);
        setIsBusinessLoading(false);
        return;
      }

      // ポジションデータ取得
      setIsPositionLoading(true);
      getPositionData(positionId).then((positionResult) => {
        if (positionResult.error.message) {
          setError(positionResult.error.message);
        } else if (positionResult.data) {
          const record = new PositionRecord(positionResult.data.Position);
          setPositionImtRecord(record);
          setInterviewImtRecord(record.Interview || new InterviewRecord({}));
          setIsOutsourcingPosition(!record.isTraitEmploymentTypeEmployee());
          setIsRegularOutsourcingPosition(
            record.isTraitEmploymentTypeRegularOutsourcing()
          );
          setIsSpotOutsourcingPosition(
            record.isTraitEmploymentTypeSpotOutsourcing()
          );
          setIsCommissionOutsourcingPosition(
            record.isTraitEmploymentTypeCommissionOutsourcing()
          );
        }
        setIsPositionLoading(false);
      });

      // 会社データ取得
      setIsCompanyLoading(true);
      getCompanyData(positionId).then((companyResult) => {
        if (companyResult.error) {
          setError(companyResult.error.message);
        } else if (companyResult.data) {
          setCompanyImtRecord(new ApplyCompanyRecord(companyResult.data));
        }
        setIsCompanyLoading(false);
      });

      // 業界データ取得
      setIsBusinessLoading(true);
      getBusinessData(positionId).then((businessResult) => {
        if (businessResult.error) {
          setError(businessResult.error.message);
        } else if (businessResult.data) {
          const record = new BusinessRecord(businessResult.data.Business);
          setBusinessImtRecord(record);
        }
        setIsBusinessLoading(false);
      });
    };

    fetchData();
  }, [positionId]);

  const isLoading = isPositionLoading || isCompanyLoading || isBusinessLoading;

  const handleChatOpen = () => setIsChatOpen(true);
  const handleChatClose = () => setIsChatOpen(false);

  /**
   * 「求人TOP」セクションを返す
   */
  const renderPositionTop = useCallback(() => {
    if (isLoading || error != null) {
      return;
    }

    return (
      <div className={styles.positionTopSection}>
        <PositionTop
          positionImtRecord={positionImtRecord}
          companyImtRecord={companyImtRecord}
          interviewImtRecord={interviewImtRecord}
          isOutsourcingPosition={isOutsourcingPosition}
          isSpotOutsourcingPosition={isSpotOutsourcingPosition}
          isCommissionOutsourcingPosition={isCommissionOutsourcingPosition}
        />
      </div>
    );
  }, [
    isLoading,
    error,
    positionImtRecord,
    companyImtRecord,
    interviewImtRecord,
    isOutsourcingPosition,
    isSpotOutsourcingPosition,
    isCommissionOutsourcingPosition,
  ]);

  /**
   * 「募集要項」セクションを返す
   */
  const renderJobDescription = useCallback(() => {
    if (isLoading || error != null) {
      return;
    }

    return (
      <li
        key={POSITION_DETAIL_TABS.JOB_DESCRIPTION}
        className={styles.sectionItem}
      >
        <JobDescription
          positionImtRecord={positionImtRecord}
          companyImtRecord={companyImtRecord}
          isOutsourcingPosition={isOutsourcingPosition}
          isRegularOutsourcingPosition={isRegularOutsourcingPosition}
          isSpotOutsourcingPosition={isSpotOutsourcingPosition}
          isCommissionOutsourcingPosition={isCommissionOutsourcingPosition}
        />
      </li>
    );
  }, [
    isLoading,
    error,
    positionImtRecord,
    companyImtRecord,
    isOutsourcingPosition,
    isRegularOutsourcingPosition,
    isSpotOutsourcingPosition,
    isCommissionOutsourcingPosition,
  ]);

  /**
   * 「選考方法」セクションを返す
   */
  // const renderInterviewSettings = useCallback(() => {
  //   const isNoInterviewForDisplay =
  //     !interviewImtRecord || interviewImtRecord.isNoInterviewForDisplay();

  //   // 業務委託求人（「ミイダス相性判定」「書類選考」は表示しない）かつ、面接情報が何もない
  //   // または
  //   // 業務委託スポット求人は選考方法の情報が何もないため表示しない
  //   if (
  //     (isOutsourcingPosition && isNoInterviewForDisplay) ||
  //     isSpotOutsourcingPosition
  //   ) {
  //     return null;
  //   }

  //   return (
  //     <li
  //       key={POSITION_DETAIL_TABS.INTERVIEW_SETTINGS}
  //       className={styles.sectionItem}
  //     >
  //       <InterviewSettings
  //         positionDetailImtRecord={positionImtRecord}
  //         interviewImtRecord={interviewImtRecord}
  //       />
  //     </li>
  //   );
  // }, [isLoading, error]);

  /**
   * 「企業情報」セクションを返す
   */
  const renderCompanyOverview = useCallback(() => {
    if (isLoading || error != null) {
      return;
    }

    return (
      <li
        key={POSITION_DETAIL_TABS.COMPANY_OVERVIEW}
        className={styles.sectionItem}
      >
        <CompanyOverview
          businessImtRecord={businessImtRecord}
          positionImtRecord={positionImtRecord}
          companyImtRecord={companyImtRecord}
          showModalBadgeDescription={() =>
            setIsBadgeDescriptionModalDisplay(true)
          }
          showModalHPMCertificationDescription={() =>
            setIsHPMCertificationDescriptionModalDisplay(true)
          }
          isOutsourcingPosition={isOutsourcingPosition}
        />
      </li>
    );
  }, [
    isLoading,
    error,
    businessImtRecord,
    positionImtRecord,
    companyImtRecord,
    isOutsourcingPosition,
  ]);

  /**
   * 「業界研究」セクションを返す
   * TODO: 日経データが必要なので、非表示
   */
  // const renderIndustryResearch = useCallback(() => {
  //   if (!this.props.isDisplayNikkeiReport) {
  //     return null;
  //   }

  //   const companyName = this.props.offerDetailCompanyImtRecord.get('Name');

  //   return (
  //     <li
  //       key={POSITION_DETAIL_TABS.INDUSTRY_RESEARCH}
  //       ref={this.industryResearchSectionRef}
  //       className={styles.sectionItem}
  //     >
  //       <IndustryResearchTab
  //         nikkeiListImtList={this.props.nikkeiListImtList}
  //         positionId={this.props.positionId}
  //         companyName={companyName}
  //       />
  //     </li>
  //   );
  // }, [isLoading, error]);

  /**
   * 求人詳細内容を返す
   */
  const renderContent = useCallback(() => {
    if (isLoading || error != null) {
      return;
    }

    const jobDescriptionSection = renderJobDescription();
    // const interviewSettings = renderInterviewSettings();
    const companyOverview = renderCompanyOverview();
    // const industryResearch = renderIndustryResearch();

    return (
      <div className="js-VisibleMeasureRoot">
        <ul className={styles.content}>
          {jobDescriptionSection}
          {/* {interviewSettings} */}
          {companyOverview}
          {/* {industryResearch} */}
        </ul>
      </div>
    );
  }, [isLoading, error, renderJobDescription, renderCompanyOverview]);

  const dispatch = useAppDispatch();
  const apply = () => {
    dispatch(
      setSessionStatus(SessionStatus.APPLYING)
    );

    router.back();
  };

  const close = () => {
    sendWebSocketMessage(
      "###PositionAdvisorEnd###",
      PAGE_NAME.POSITION_DETAIL,
      PAGE_NAME.CHAT,
      positionId
    );

    router.back();
  };

  return (
    <>
      <Box
        sx={{
          width: "100vw",
          height: "100vh",
          p: 2,
          position: "relative",
          overflow: "auto",
        }}
      >
        <Box sx={{ mb: 3, mt: 2, textAlign: "center" }}>
          <Typography variant="h4" component="h1" fontWeight="bold">
            {!isLoading && companyImtRecord
              ? companyImtRecord.get("Name")
              : "求人詳細"}
          </Typography>
        </Box>
        <Box
          sx={{
            position: "absolute",
            top: 8,
            right: 8,
            zIndex: 10,
            display: "flex",
            gap: 1,
          }}
        >
          <Button onClick={close}>閉じる</Button>
          <Button onClick={apply}>応募</Button>
        </Box>

        {/* ポジション詳細取得中 */}
        {isLoading && (
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              position: "fixed",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: "rgba(255, 255, 255, 0.7)",
              zIndex: 1000,
            }}
          >
            <CircularProgress size={60} />
          </Box>
        )}

        {error && (
          <Box sx={{ mb: 3, p: 2, bgcolor: "#ffebee", borderRadius: 1 }}>
            <Typography color="error">{error}</Typography>
          </Box>
        )}

        {renderPositionTop()}
        {renderContent()}

        {!isLoading && !error && (
          <>
            {/* 左下のチャットボタン */}
            <Fab
              color="primary"
              aria-label="chat"
              onClick={handleChatOpen}
              sx={{
                position: "fixed",
                bottom: 20,
                left: 20,
              }}
              className={styles.blob}
            >
              <ChatIcon />
            </Fab>

            {/* チャットモーダル */}
            <Modal
              open={isChatOpen}
              onClose={handleChatClose}
              aria-labelledby="chat-modal-title"
              aria-describedby="chat-modal-description"
              keepMounted
            >
              <Box
                sx={{
                  position: "absolute",
                  bottom: 0,
                  right: 0,
                  width: { xs: "100%", sm: 400 },
                  height: { xs: "80vh", sm: "70vh" },
                  bgcolor: "background.paper",
                  boxShadow: 24,
                  borderTopLeftRadius: { xs: 16, sm: 16 },
                  borderTopRightRadius: { xs: 16, sm: 0 },
                  display: "flex",
                  flexDirection: "column",
                }}
              >
                <Chat
                  currentPage={PAGE_NAME.POSITION_DETAIL}
                  positionID={positionId}
                />
              </Box>
            </Modal>
          </>
        )}
      </Box>
      <ModalBadgeDescription
        isDisplay={isBadgeDescriptionModalDisplay}
        hideModal={() => setIsBadgeDescriptionModalDisplay(false)}
      />
      <ModalHPMCertificationDescription
        isDisplay={isHPMCertificationDescriptionModalDisplay}
        hideModal={() => setIsHPMCertificationDescriptionModalDisplay(false)}
      />
    </>
  );
}

export default function PositionDetailPage() {
  return (
    <Suspense fallback={<CircularProgress />}>
      <PositionDetail />
    </Suspense>
  );
}
